目标： 列出项目实际代码的包依赖以及包依赖的所有嵌套依赖，得到包列表 list，然后再查找这些包是否有版本冲突。

实施步骤将分为三步：
1. 先分析项目代码中实际引用到的 npm 包。方案是安装：npm install -g depcheck，然后执行depcheck;输出结果有些问题，需过滤掉一些webpack 定义的别名alias
2. 对第一步的结果，深入分析这些包的 dependencies 的依赖，需嵌套分析。方案是npm ls --production --all --json | jq -r '.. | .dependencies? | objects | to_entries[] | select(.key == "@tarojs/mobx-h5") | .value | .. | .dependencies? | objects | keys_unsorted[]' | sort -u > taro_mobx_h5_dependencies.txt
3. 对第二步的结果，查找这些包是否有版本冲突。方案是遍历 package-lock 文件，去重后输出，代码可参考 cli.js 和 find-duplicate-dependencies.js

我要将这三步整合成一个命令，你现在可以使用 task view 功能一步步去分析和实现了。
This package has the following duplicate dependencies:

file-type:
[
  {
    name: 'file-type',
    version: '12.4.2',
    from: 'file-type@^12.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin'
  },
  {
    name: 'file-type',
    version: '5.2.0',
    from: 'file-type@^5.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar'
  },
  {
    name: 'file-type',
    version: '6.2.0',
    from: 'file-type@^6.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tarbz2'
  },
  {
    name: 'file-type',
    version: '3.9.0',
    from: 'file-type@^3.8.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-unzip'
  },
  {
    name: 'file-type',
    version: '4.4.0',
    from: 'file-type@^4.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/archive-type'
  },
  {
    name: 'file-type',
    version: '8.1.0',
    from: 'file-type@^8.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download'
  },
  {
    name: 'file-type',
    version: '10.11.0',
    from: 'file-type@^10.4.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/is-gif'
  }
] 

globby:
[
  {
    name: 'globby',
    version: '10.0.2',
    from: 'globby@^10.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin'
  },
  {
    name: 'globby',
    version: '7.1.1',
    from: 'globby@^7.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin'
  },
  {
    name: 'globby',
    version: '6.1.0',
    from: 'globby@^6.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/del'
  }
] 

array-union:
[
  {
    name: 'array-union',
    version: '2.1.0',
    from: 'array-union@^2.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'array-union',
    version: '1.0.2',
    from: 'array-union@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/globby'
  }
] 

dir-glob:
[
  {
    name: 'dir-glob',
    version: '3.0.1',
    from: 'dir-glob@^3.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'dir-glob',
    version: '2.2.2',
    from: 'dir-glob@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/globby'
  }
] 

path-type:
[
  {
    name: 'path-type',
    version: '4.0.0',
    from: 'path-type@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby/dir-glob'
  },
  {
    name: 'path-type',
    version: '1.1.0',
    from: 'path-type@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/meow/read-pkg-up/read-pkg'
  },
  {
    name: 'path-type',
    version: '3.0.0',
    from: 'path-type@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/globby/dir-glob'
  }
] 

glob-parent:
[
  {
    name: 'glob-parent',
    version: '5.1.2',
    from: 'glob-parent@^5.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby/fast-glob'
  },
  {
    name: 'glob-parent',
    version: '3.1.0',
    from: 'glob-parent@^3.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  }
] 

micromatch:
[
  {
    name: 'micromatch',
    version: '4.0.8',
    from: 'micromatch@^4.0.8',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby/fast-glob'
  },
  {
    name: 'micromatch',
    version: '3.1.10',
    from: 'micromatch@^3.1.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch'
  }
] 

glob:
[
  {
    name: 'glob',
    version: '7.2.3',
    from: 'glob@^7.1.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'glob',
    version: '7.1.7',
    from: 'glob@~7.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/gaze/globule'
  },
  {
    name: 'glob',
    version: '7.0.6',
    from: 'glob@7.0.x',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/stylus'
  }
] 

ignore:
[
  {
    name: 'ignore',
    version: '5.3.2',
    from: 'ignore@^5.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'ignore',
    version: '3.3.10',
    from: 'ignore@^3.3.5',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/globby'
  }
] 

slash:
[
  {
    name: 'slash',
    version: '3.0.0',
    from: 'slash@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'slash',
    version: '1.0.0',
    from: 'slash@1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core'
  },
  {
    name: 'slash',
    version: '2.0.0',
    from: 'slash@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-include-assets-plugin'
  }
] 

make-dir:
[
  {
    name: 'make-dir',
    version: '3.1.0',
    from: 'make-dir@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin'
  },
  {
    name: 'make-dir',
    version: '1.3.0',
    from: 'make-dir@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress'
  },
  {
    name: 'make-dir',
    version: '2.1.0',
    from: 'make-dir@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir'
  }
] 

semver:
[
  {
    name: 'semver',
    version: '6.3.1',
    from: 'semver@^6.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/make-dir'
  },
  {
    name: 'semver',
    version: '5.7.2',
    from: 'semver@^5.5.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa/cross-spawn'
  },
  {
    name: 'semver',
    version: '5.3.0',
    from: 'semver@~5.3.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/node-gyp'
  }
] 

execa:
[
  {
    name: 'execa',
    version: '0.7.0',
    from: 'execa@^0.7.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer'
  },
  {
    name: 'execa',
    version: '1.0.0',
    from: 'execa@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version'
  },
  {
    name: 'execa',
    version: '0.10.0',
    from: 'execa@^0.10.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-pngquant/pngquant-bin'
  }
] 

cross-spawn:
[
  {
    name: 'cross-spawn',
    version: '5.1.0',
    from: 'cross-spawn@^5.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer/execa'
  },
  {
    name: 'cross-spawn',
    version: '6.0.6',
    from: 'cross-spawn@^6.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa'
  },
  {
    name: 'cross-spawn',
    version: '3.0.1',
    from: 'cross-spawn@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass'
  }
] 

lru-cache:
[
  {
    name: 'lru-cache',
    version: '4.1.5',
    from: 'lru-cache@^4.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer/execa/cross-spawn'
  },
  {
    name: 'lru-cache',
    version: '5.1.1',
    from: 'lru-cache@^5.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  }
] 

get-stream:
[
  {
    name: 'get-stream',
    version: '3.0.0',
    from: 'get-stream@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer/execa'
  },
  {
    name: 'get-stream',
    version: '2.3.1',
    from: 'get-stream@^2.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-unzip'
  },
  {
    name: 'get-stream',
    version: '4.1.0',
    from: 'get-stream@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa'
  }
] 

is-stream:
[
  {
    name: 'is-stream',
    version: '1.1.0',
    from: 'is-stream@^1.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer/execa'
  },
  {
    name: 'is-stream',
    version: '2.0.1',
    from: 'is-stream@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-pngquant'
  }
] 

pify:
[
  {
    name: 'pify',
    version: '3.0.0',
    from: 'pify@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer'
  },
  {
    name: 'pify',
    version: '2.3.0',
    from: 'pify@^2.3.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-unzip'
  },
  {
    name: 'pify',
    version: '4.0.1',
    from: 'pify@^4.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper'
  }
] 

readable-stream:
[
  {
    name: 'readable-stream',
    version: '2.3.8',
    from: 'readable-stream@^2.3.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar/tar-stream/bl'
  },
  {
    name: 'readable-stream',
    version: '3.6.2',
    from: 'readable-stream@^3.0.6',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/spdy/spdy-transport'
  }
] 

safe-buffer:
[
  {
    name: 'safe-buffer',
    version: '5.2.1',
    from: 'safe-buffer@^5.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar/tar-stream/bl'
  },
  {
    name: 'safe-buffer',
    version: '5.1.2',
    from: 'safe-buffer@~5.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/memory-fs/readable-stream'
  }
] 

commander:
[
  {
    name: 'commander',
    version: '2.20.3',
    from: 'commander@^2.8.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tarbz2/seek-bzip'
  },
  {
    name: 'commander',
    version: '2.17.1',
    from: 'commander@2.17.x',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/html-minifier'
  },
  {
    name: 'commander',
    version: '2.19.0',
    from: 'commander@~2.19.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/html-minifier/uglify-js'
  },
  {
    name: 'commander',
    version: '2.14.1',
    from: 'commander@~2.14.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin/uglify-es'
  }
] 

buffer:
[
  {
    name: 'buffer',
    version: '5.7.1',
    from: 'buffer@^5.2.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tarbz2/unbzip2-stream'
  },
  {
    name: 'buffer',
    version: '4.9.2',
    from: 'buffer@^4.3.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser'
  }
] 

download:
[
  {
    name: 'download',
    version: '6.2.5',
    from: 'download@^6.2.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build'
  },
  {
    name: 'download',
    version: '7.1.0',
    from: 'download@^7.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper'
  }
] 

mime-db:
[
  {
    name: 'mime-db',
    version: '1.54.0',
    from: 'mime-db@^1.28.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/ext-name/ext-list'
  },
  {
    name: 'mime-db',
    version: '1.52.0',
    from: 'mime-db@1.52.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/request/mime-types'
  }
] 

sort-keys:
[
  {
    name: 'sort-keys',
    version: '1.1.2',
    from: 'sort-keys@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/ext-name/sort-keys-length'
  },
  {
    name: 'sort-keys',
    version: '2.0.0',
    from: 'sort-keys@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/cacheable-request/normalize-url'
  }
] 

got:
[
  {
    name: 'got',
    version: '7.1.0',
    from: 'got@^7.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download'
  },
  {
    name: 'got',
    version: '8.3.2',
    from: 'got@^8.3.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download'
  }
] 

lowercase-keys:
[
  {
    name: 'lowercase-keys',
    version: '1.0.1',
    from: 'lowercase-keys@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'lowercase-keys',
    version: '1.0.0',
    from: 'lowercase-keys@1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/cacheable-request'
  }
] 

p-cancelable:
[
  {
    name: 'p-cancelable',
    version: '0.3.0',
    from: 'p-cancelable@^0.3.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'p-cancelable',
    version: '0.4.1',
    from: 'p-cancelable@^0.4.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got'
  }
] 

p-timeout:
[
  {
    name: 'p-timeout',
    version: '1.2.1',
    from: 'p-timeout@^1.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'p-timeout',
    version: '2.0.1',
    from: 'p-timeout@^2.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got'
  }
] 

url-parse-lax:
[
  {
    name: 'url-parse-lax',
    version: '1.0.0',
    from: 'url-parse-lax@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'url-parse-lax',
    version: '3.0.0',
    from: 'url-parse-lax@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got'
  }
] 

prepend-http:
[
  {
    name: 'prepend-http',
    version: '1.0.4',
    from: 'prepend-http@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got/url-parse-lax'
  },
  {
    name: 'prepend-http',
    version: '2.0.0',
    from: 'prepend-http@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/url-parse-lax'
  }
] 

p-event:
[
  {
    name: 'p-event',
    version: '1.3.0',
    from: 'p-event@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download'
  },
  {
    name: 'p-event',
    version: '2.3.1',
    from: 'p-event@^2.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download'
  }
] 

pump:
[
  {
    name: 'pump',
    version: '3.0.3',
    from: 'pump@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa/get-stream'
  },
  {
    name: 'pump',
    version: '2.0.1',
    from: 'pump@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi/pumpify'
  }
] 

p-is-promise:
[
  {
    name: 'p-is-promise',
    version: '1.1.0',
    from: 'p-is-promise@^1.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/into-stream'
  },
  {
    name: 'p-is-promise',
    version: '2.1.0',
    from: 'p-is-promise@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale/mem'
  }
] 

chalk:
[
  {
    name: 'chalk',
    version: '1.1.3',
    from: 'chalk@1.1.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/logalot/squeak'
  },
  {
    name: 'chalk',
    version: '2.4.2',
    from: 'chalk@^2.4.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa'
  }
] 

ansi-styles:
[
  {
    name: 'ansi-styles',
    version: '3.2.1',
    from: 'ansi-styles@^3.2.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa/chalk'
  },
  {
    name: 'ansi-styles',
    version: '2.2.1',
    from: 'ansi-styles@2.2.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/chalk'
  }
] 

supports-color:
[
  {
    name: 'supports-color',
    version: '5.5.0',
    from: 'supports-color@^5.3.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa/chalk'
  },
  {
    name: 'supports-color',
    version: '2.0.0',
    from: 'supports-color@2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/chalk'
  },
  {
    name: 'supports-color',
    version: '3.2.3',
    from: 'supports-color@^3.2.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-pxtransform/postcss-pxtorem/postcss'
  }
] 

has-flag:
[
  {
    name: 'has-flag',
    version: '3.0.0',
    from: 'has-flag@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa/chalk/supports-color'
  },
  {
    name: 'has-flag',
    version: '1.0.0',
    from: 'has-flag@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-pxtransform/postcss-pxtorem/postcss/supports-color'
  }
] 

css-select:
[
  {
    name: 'css-select',
    version: '2.1.0',
    from: 'css-select@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'css-select',
    version: '4.3.0',
    from: 'css-select@^4.1.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid'
  }
] 

css-what:
[
  {
    name: 'css-what',
    version: '3.4.2',
    from: 'css-what@^3.2.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select'
  },
  {
    name: 'css-what',
    version: '6.1.0',
    from: 'css-what@^6.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/css-select'
  }
] 

domutils:
[
  {
    name: 'domutils',
    version: '1.7.0',
    from: 'domutils@^1.7.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select'
  },
  {
    name: 'domutils',
    version: '2.8.0',
    from: 'domutils@^2.5.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/htmlparser2'
  }
] 

dom-serializer:
[
  {
    name: 'dom-serializer',
    version: '0.2.2',
    from: 'dom-serializer@0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select/domutils'
  },
  {
    name: 'dom-serializer',
    version: '1.4.1',
    from: 'dom-serializer@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/htmlparser2/domutils'
  }
] 

domelementtype:
[
  {
    name: 'domelementtype',
    version: '2.3.0',
    from: 'domelementtype@^2.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select/domutils/dom-serializer'
  },
  {
    name: 'domelementtype',
    version: '1.3.1',
    from: 'domelementtype@1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select/domutils'
  }
] 

entities:
[
  {
    name: 'entities',
    version: '2.2.0',
    from: 'entities@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select/domutils/dom-serializer'
  },
  {
    name: 'entities',
    version: '1.1.2',
    from: 'entities@^1.1.1',
    path: 'muwa1-ts-project/@mu/zui/@mu/mini-html-parser2'
  }
] 

nth-check:
[
  {
    name: 'nth-check',
    version: '1.0.2',
    from: 'nth-check@^1.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select'
  },
  {
    name: 'nth-check',
    version: '2.1.1',
    from: 'nth-check@^2.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/css-select'
  }
] 

css-tree:
[
  {
    name: 'css-tree',
    version: '1.0.0-alpha.37',
    from: 'css-tree@1.0.0-alpha.37',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'css-tree',
    version: '1.1.3',
    from: 'css-tree@^1.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/csso'
  },
  {
    name: 'css-tree',
    version: '1.0.0-alpha.29',
    from: 'css-tree@1.0.0-alpha.29',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-csso/csso'
  }
] 

mdn-data:
[
  {
    name: 'mdn-data',
    version: '2.0.4',
    from: 'mdn-data@2.0.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-tree'
  },
  {
    name: 'mdn-data',
    version: '2.0.14',
    from: 'mdn-data@2.0.14',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/csso/css-tree'
  },
  {
    name: 'mdn-data',
    version: '1.1.4',
    from: 'mdn-data@~1.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-csso/csso/css-tree'
  }
] 

source-map:
[
  {
    name: 'source-map',
    version: '0.6.1',
    from: 'source-map@^0.6.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-tree'
  },
  {
    name: 'source-map',
    version: '0.5.7',
    from: 'source-map@^0.5.6',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon'
  },
  {
    name: 'source-map',
    version: '0.4.4',
    from: 'source-map@^0.4.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/scss-tokenizer'
  },
  {
    name: 'source-map',
    version: '0.7.4',
    from: 'source-map@^0.7.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus/stylus'
  },
  {
    name: 'source-map',
    version: '0.1.43',
    from: 'source-map@0.1.x',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/stylus'
  }
] 

csso:
[
  {
    name: 'csso',
    version: '4.2.0',
    from: 'csso@^4.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'csso',
    version: '3.5.1',
    from: 'csso@^3.5.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-csso'
  }
] 

mkdirp:
[
  {
    name: 'mkdirp',
    version: '0.5.6',
    from: 'mkdirp@0.5.6',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'mkdirp',
    version: '1.0.4',
    from: 'mkdirp@~1.0.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus/stylus'
  }
] 

sax:
[
  {
    name: 'sax',
    version: '1.2.4',
    from: 'sax@~1.2.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'sax',
    version: '0.5.8',
    from: 'sax@0.5.x',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/stylus'
  }
] 

util.promisify:
[
  {
    name: 'util.promisify',
    version: '1.0.1',
    from: 'util.promisify@~1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'util.promisify',
    version: '1.0.0',
    from: 'util.promisify@1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin'
  }
] 

isarray:
[
  {
    name: 'isarray',
    version: '2.0.5',
    from: 'isarray@2.0.5',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract/safe-push-apply'
  },
  {
    name: 'isarray',
    version: '1.0.0',
    from: 'isarray@~1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/memory-fs/readable-stream'
  }
] 

loader-utils:
[
  {
    name: 'loader-utils',
    version: '1.4.2',
    from: 'loader-utils@^1.2.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader'
  },
  {
    name: 'loader-utils',
    version: '0.2.17',
    from: 'loader-utils@^0.2.16',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin'
  }
] 

big.js:
[
  {
    name: 'big.js',
    version: '5.2.2',
    from: 'big.js@^5.2.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/loader-utils'
  },
  {
    name: 'big.js',
    version: '3.2.0',
    from: 'big.js@^3.1.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/loader-utils'
  }
] 

emojis-list:
[
  {
    name: 'emojis-list',
    version: '3.0.0',
    from: 'emojis-list@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/loader-utils'
  },
  {
    name: 'emojis-list',
    version: '2.1.0',
    from: 'emojis-list@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/loader-utils'
  }
] 

json5:
[
  {
    name: 'json5',
    version: '1.0.2',
    from: 'json5@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/image-webpack-loader/loader-utils'
  },
  {
    name: 'json5',
    version: '0.5.1',
    from: 'json5@0.5.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core'
  }
] 

debug:
[
  {
    name: 'debug',
    version: '4.4.1',
    from: 'debug@^4.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/script-ext-html-webpack-plugin'
  },
  {
    name: 'debug',
    version: '2.6.9',
    from: 'debug@^2.3.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob/expand-brackets'
  },
  {
    name: 'debug',
    version: '3.1.0',
    from: 'debug@~3.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus/stylus'
  },
  {
    name: 'debug',
    version: '3.2.7',
    from: 'debug@^3.2.5',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/sockjs-client'
  }
] 

ms:
[
  {
    name: 'ms',
    version: '2.1.3',
    from: 'ms@^2.1.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/script-ext-html-webpack-plugin/debug'
  },
  {
    name: 'ms',
    version: '2.0.0',
    from: 'ms@2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob/expand-brackets/debug'
  }
] 

cacache:
[
  {
    name: 'cacache',
    version: '12.0.4',
    from: 'cacache@^12.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'cacache',
    version: '10.0.4',
    from: 'cacache@^10.0.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin'
  }
] 

yallist:
[
  {
    name: 'yallist',
    version: '3.1.1',
    from: 'yallist@^3.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/lru-cache'
  },
  {
    name: 'yallist',
    version: '2.1.2',
    from: 'yallist@^2.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/cross-spawn/lru-cache'
  }
] 

inherits:
[
  {
    name: 'inherits',
    version: '2.0.4',
    from: 'inherits@2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/glob'
  },
  {
    name: 'inherits',
    version: '2.0.3',
    from: 'inherits@2.0.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/assert/util'
  }
] 

minimatch:
[
  {
    name: 'minimatch',
    version: '3.1.2',
    from: 'minimatch@3.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/glob'
  },
  {
    name: 'minimatch',
    version: '3.0.8',
    from: 'minimatch@~3.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/gaze/globule/glob'
  }
] 

mississippi:
[
  {
    name: 'mississippi',
    version: '3.0.0',
    from: 'mississippi@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  },
  {
    name: 'mississippi',
    version: '2.0.0',
    from: 'mississippi@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin/cacache'
  }
] 

ssri:
[
  {
    name: 'ssri',
    version: '6.0.2',
    from: 'ssri@^6.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  },
  {
    name: 'ssri',
    version: '5.3.0',
    from: 'ssri@^5.2.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin/cacache'
  }
] 

y18n:
[
  {
    name: 'y18n',
    version: '4.0.3',
    from: 'y18n@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  },
  {
    name: 'y18n',
    version: '3.2.2',
    from: 'y18n@^3.2.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs'
  }
] 

find-cache-dir:
[
  {
    name: 'find-cache-dir',
    version: '2.1.0',
    from: 'find-cache-dir@^2.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'find-cache-dir',
    version: '1.0.0',
    from: 'find-cache-dir@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader'
  }
] 

pkg-dir:
[
  {
    name: 'pkg-dir',
    version: '3.0.0',
    from: 'pkg-dir@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir'
  },
  {
    name: 'pkg-dir',
    version: '2.0.0',
    from: 'pkg-dir@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir'
  }
] 

find-up:
[
  {
    name: 'find-up',
    version: '3.0.0',
    from: 'find-up@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir/pkg-dir'
  },
  {
    name: 'find-up',
    version: '1.1.2',
    from: 'find-up@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/meow/read-pkg-up'
  },
  {
    name: 'find-up',
    version: '2.1.0',
    from: 'find-up@^2.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir'
  }
] 

locate-path:
[
  {
    name: 'locate-path',
    version: '3.0.0',
    from: 'locate-path@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir/pkg-dir/find-up'
  },
  {
    name: 'locate-path',
    version: '2.0.0',
    from: 'locate-path@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up'
  }
] 

path-exists:
[
  {
    name: 'path-exists',
    version: '3.0.0',
    from: 'path-exists@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir/pkg-dir/find-up/locate-path'
  },
  {
    name: 'path-exists',
    version: '2.1.0',
    from: 'path-exists@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/meow/read-pkg-up/find-up'
  }
] 

p-locate:
[
  {
    name: 'p-locate',
    version: '3.0.0',
    from: 'p-locate@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir/pkg-dir/find-up/locate-path'
  },
  {
    name: 'p-locate',
    version: '2.0.0',
    from: 'p-locate@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up/locate-path'
  }
] 

p-limit:
[
  {
    name: 'p-limit',
    version: '2.3.0',
    from: 'p-limit@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir/pkg-dir/find-up/locate-path/p-locate'
  },
  {
    name: 'p-limit',
    version: '1.3.0',
    from: 'p-limit@^1.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up/locate-path/p-locate'
  }
] 

schema-utils:
[
  {
    name: 'schema-utils',
    version: '1.0.0',
    from: 'schema-utils@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'schema-utils',
    version: '0.4.7',
    from: 'schema-utils@^0.4.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack'
  }
] 

serialize-javascript:
[
  {
    name: 'serialize-javascript',
    version: '4.0.0',
    from: 'serialize-javascript@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'serialize-javascript',
    version: '1.9.1',
    from: 'serialize-javascript@^1.4.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin'
  }
] 

source-map-support:
[
  {
    name: 'source-map-support',
    version: '0.5.21',
    from: 'source-map-support@~0.5.12',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/terser'
  },
  {
    name: 'source-map-support',
    version: '0.4.18',
    from: 'source-map-support@0.4.18',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-register'
  }
] 

punycode:
[
  {
    name: 'punycode',
    version: '2.3.1',
    from: 'punycode@^2.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/ajv/uri-js'
  },
  {
    name: 'punycode',
    version: '1.4.1',
    from: 'punycode@^1.2.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser'
  }
] 

memory-fs:
[
  {
    name: 'memory-fs',
    version: '0.5.0',
    from: 'memory-fs@^0.5.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/enhanced-resolve'
  },
  {
    name: 'memory-fs',
    version: '0.4.1',
    from: 'memory-fs@~0.4.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack'
  }
] 

estraverse:
[
  {
    name: 'estraverse',
    version: '5.3.0',
    from: 'estraverse@^5.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/eslint-scope/esrecurse'
  },
  {
    name: 'estraverse',
    version: '4.3.0',
    from: 'estraverse@^4.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/eslint-scope'
  }
] 

core-util-is:
[
  {
    name: 'core-util-is',
    version: '1.0.3',
    from: 'core-util-is@~1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/memory-fs/readable-stream'
  },
  {
    name: 'core-util-is',
    version: '1.0.2',
    from: 'core-util-is@1.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/request/http-signature/jsprim/verror'
  }
] 

util:
[
  {
    name: 'util',
    version: '0.10.4',
    from: 'util@^0.10.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/assert'
  },
  {
    name: 'util',
    version: '0.11.1',
    from: 'util@^0.11.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser'
  }
] 

create-hash:
[
  {
    name: 'create-hash',
    version: '1.2.0',
    from: 'create-hash@^1.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/browserify-cipher/browserify-aes'
  },
  {
    name: 'create-hash',
    version: '1.1.3',
    from: 'create-hash@~1.1.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/pbkdf2'
  }
] 

bn.js:
[
  {
    name: 'bn.js',
    version: '5.2.2',
    from: 'bn.js@^5.2.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/browserify-sign'
  },
  {
    name: 'bn.js',
    version: '4.12.2',
    from: 'bn.js@^4.11.9',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/browserify-sign/elliptic'
  }
] 

hash-base:
[
  {
    name: 'hash-base',
    version: '3.0.5',
    from: 'hash-base@~3.0.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/browserify-sign'
  },
  {
    name: 'hash-base',
    version: '2.0.2',
    from: 'hash-base@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/pbkdf2/ripemd160'
  }
] 

ripemd160:
[
  {
    name: 'ripemd160',
    version: '2.0.2',
    from: 'ripemd160@^2.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/create-hash'
  },
  {
    name: 'ripemd160',
    version: '2.0.1',
    from: 'ripemd160@=2.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/pbkdf2/create-hash'
  }
] 

qs:
[
  {
    name: 'qs',
    version: '6.14.0',
    from: 'qs@^6.12.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/url'
  },
  {
    name: 'qs',
    version: '6.5.3',
    from: 'qs@~6.5.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/request'
  },
  {
    name: 'qs',
    version: '6.13.0',
    from: 'qs@6.13.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/body-parser'
  }
] 

chokidar:
[
  {
    name: 'chokidar',
    version: '3.6.0',
    from: 'chokidar@^3.4.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack'
  },
  {
    name: 'chokidar',
    version: '2.1.8',
    from: 'chokidar@^2.1.8',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2'
  },
  {
    name: 'chokidar',
    version: '4.0.3',
    from: 'chokidar@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/sass'
  }
] 

anymatch:
[
  {
    name: 'anymatch',
    version: '3.1.3',
    from: 'anymatch@~3.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'anymatch',
    version: '2.0.0',
    from: 'anymatch@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  }
] 

normalize-path:
[
  {
    name: 'normalize-path',
    version: '3.0.0',
    from: 'normalize-path@~3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/anymatch'
  },
  {
    name: 'normalize-path',
    version: '2.1.1',
    from: 'normalize-path@^2.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch'
  }
] 

braces:
[
  {
    name: 'braces',
    version: '3.0.3',
    from: 'braces@^3.0.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'braces',
    version: '2.3.2',
    from: 'braces@^2.3.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch/micromatch'
  }
] 

fill-range:
[
  {
    name: 'fill-range',
    version: '7.1.1',
    from: 'fill-range@^7.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/braces'
  },
  {
    name: 'fill-range',
    version: '4.0.0',
    from: 'fill-range@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces'
  }
] 

to-regex-range:
[
  {
    name: 'to-regex-range',
    version: '5.0.1',
    from: 'to-regex-range@^5.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/braces/fill-range'
  },
  {
    name: 'to-regex-range',
    version: '2.1.1',
    from: 'to-regex-range@^2.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces/fill-range'
  }
] 

is-number:
[
  {
    name: 'is-number',
    version: '7.0.0',
    from: 'is-number@^7.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/braces/fill-range/to-regex-range'
  },
  {
    name: 'is-number',
    version: '3.0.0',
    from: 'is-number@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces/fill-range'
  }
] 

fsevents:
[
  {
    name: 'fsevents',
    version: '2.3.3',
    from: 'fsevents@~2.3.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'fsevents',
    version: '1.2.13',
    from: 'fsevents@^1.2.7',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  }
] 

is-glob:
[
  {
    name: 'is-glob',
    version: '4.0.3',
    from: 'is-glob@^4.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/glob-parent'
  },
  {
    name: 'is-glob',
    version: '3.1.0',
    from: 'is-glob@^3.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/glob-parent'
  }
] 

is-binary-path:
[
  {
    name: 'is-binary-path',
    version: '2.1.0',
    from: 'is-binary-path@~2.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'is-binary-path',
    version: '1.0.1',
    from: 'is-binary-path@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  }
] 

binary-extensions:
[
  {
    name: 'binary-extensions',
    version: '2.3.0',
    from: 'binary-extensions@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/is-binary-path'
  },
  {
    name: 'binary-extensions',
    version: '1.13.1',
    from: 'binary-extensions@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/is-binary-path'
  }
] 

readdirp:
[
  {
    name: 'readdirp',
    version: '3.6.0',
    from: 'readdirp@~3.6.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'readdirp',
    version: '2.2.1',
    from: 'readdirp@^2.2.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  },
  {
    name: 'readdirp',
    version: '4.1.2',
    from: 'readdirp@^4.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/sass/chokidar'
  }
] 

define-property:
[
  {
    name: 'define-property',
    version: '2.0.2',
    from: 'define-property@^2.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch/micromatch'
  },
  {
    name: 'define-property',
    version: '0.2.5',
    from: 'define-property@^0.2.5',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob/expand-brackets'
  },
  {
    name: 'define-property',
    version: '1.0.0',
    from: 'define-property@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob'
  }
] 

extend-shallow:
[
  {
    name: 'extend-shallow',
    version: '3.0.2',
    from: 'extend-shallow@^3.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch/micromatch'
  },
  {
    name: 'extend-shallow',
    version: '2.0.1',
    from: 'extend-shallow@^2.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces'
  }
] 

kind-of:
[
  {
    name: 'kind-of',
    version: '6.0.3',
    from: 'kind-of@^6.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch/micromatch'
  },
  {
    name: 'kind-of',
    version: '3.2.2',
    from: 'kind-of@^3.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces/fill-range/is-number'
  },
  {
    name: 'kind-of',
    version: '4.0.0',
    from: 'kind-of@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/has-value/has-values'
  },
  {
    name: 'kind-of',
    version: '5.1.0',
    from: 'kind-of@^5.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/sass-loader/clone-deep/shallow-clone'
  }
] 

isobject:
[
  {
    name: 'isobject',
    version: '3.0.1',
    from: 'isobject@^3.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces'
  },
  {
    name: 'isobject',
    version: '2.1.0',
    from: 'isobject@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/unset-value/has-value'
  }
] 

is-extendable:
[
  {
    name: 'is-extendable',
    version: '0.1.1',
    from: 'is-extendable@^0.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces/extend-shallow'
  },
  {
    name: 'is-extendable',
    version: '1.0.1',
    from: 'is-extendable@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/extend-shallow'
  }
] 

is-descriptor:
[
  {
    name: 'is-descriptor',
    version: '1.0.3',
    from: 'is-descriptor@^1.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/define-property'
  },
  {
    name: 'is-descriptor',
    version: '0.1.7',
    from: 'is-descriptor@^0.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob/expand-brackets/define-property'
  }
] 

has-value:
[
  {
    name: 'has-value',
    version: '1.0.0',
    from: 'has-value@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base'
  },
  {
    name: 'has-value',
    version: '0.3.1',
    from: 'has-value@^0.3.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/unset-value'
  }
] 

has-values:
[
  {
    name: 'has-values',
    version: '1.0.0',
    from: 'has-values@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/has-value'
  },
  {
    name: 'has-values',
    version: '0.1.4',
    from: 'has-values@^0.1.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/unset-value/has-value'
  }
] 

for-in:
[
  {
    name: 'for-in',
    version: '1.0.2',
    from: 'for-in@^1.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/mixin-deep'
  },
  {
    name: 'for-in',
    version: '0.1.8',
    from: 'for-in@^0.1.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/sass-loader/clone-deep/shallow-clone/mixin-object'
  }
] 

intersection-observer:
[
  {
    name: 'intersection-observer',
    version: '0.12.0',
    from: 'intersection-observer@0.12.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/leda'
  },
  {
    name: 'intersection-observer',
    version: '0.7.0',
    from: 'intersection-observer@^0.7.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/components'
  }
] 

js-tokens:
[
  {
    name: 'js-tokens',
    version: '4.0.0',
    from: 'js-tokens@4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@babel/plugin-proposal-class-properties/@babel/helper-create-class-features-plugin/@babel/traverse/@babel/code-frame'
  },
  {
    name: 'js-tokens',
    version: '3.0.2',
    from: 'js-tokens@3.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-code-frame'
  }
] 

jsesc:
[
  {
    name: 'jsesc',
    version: '3.1.0',
    from: 'jsesc@^3.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@babel/plugin-proposal-class-properties/@babel/helper-create-class-features-plugin/@babel/traverse/@babel/generator'
  },
  {
    name: 'jsesc',
    version: '3.0.2',
    from: 'jsesc@~3.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@babel/preset-env/@babel/plugin-syntax-unicode-sets-regex/@babel/helper-create-regexp-features-plugin/regexpu-core/regjsparser'
  },
  {
    name: 'jsesc',
    version: '1.3.0',
    from: 'jsesc@1.3.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-generator'
  }
] 

globals:
[
  {
    name: 'globals',
    version: '11.12.0',
    from: 'globals@^11.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@babel/plugin-proposal-class-properties/@babel/helper-create-class-features-plugin/@babel/traverse'
  },
  {
    name: 'globals',
    version: '9.18.0',
    from: 'globals@9.18.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-traverse'
  }
] 

browserslist:
[
  {
    name: 'browserslist',
    version: '4.25.0',
    from: 'browserslist@^4.24.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@babel/plugin-proposal-object-rest-spread/@babel/helper-compilation-targets'
  },
  {
    name: 'browserslist',
    version: '3.2.8',
    from: 'browserslist@^3.2.8',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/autoprefixer'
  }
] 

resolve:
[
  {
    name: 'resolve',
    version: '1.22.10',
    from: 'resolve@^1.10.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@babel/preset-env/babel-plugin-polyfill-corejs2/@babel/helper-define-polyfill-provider'
  },
  {
    name: 'resolve',
    version: '1.8.1',
    from: 'resolve@1.8.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  }
] 

whatwg-fetch:
[
  {
    name: 'whatwg-fetch',
    version: '3.0.0',
    from: 'whatwg-fetch@3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@mu/madp-fetch'
  },
  {
    name: 'whatwg-fetch',
    version: '2.0.4',
    from: 'whatwg-fetch@^2.0.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/taro-h5'
  }
] 

regenerator-runtime:
[
  {
    name: 'regenerator-runtime',
    version: '0.11.1',
    from: 'regenerator-runtime@0.11.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/async-await'
  },
  {
    name: 'regenerator-runtime',
    version: '0.13.11',
    from: 'regenerator-runtime@^0.13.2',
    path: 'muwa1-ts-project/@mu/zui/@babel/polyfill'
  }
] 

classnames:
[
  {
    name: 'classnames',
    version: '2.5.1',
    from: 'classnames@^2.2.5',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/components'
  },
  {
    name: 'classnames',
    version: '2.2.6',
    from: 'classnames@2.2.6',
    path: 'muwa1-ts-project/@mu/zui'
  }
] 

resolve-pathname:
[
  {
    name: 'resolve-pathname',
    version: '3.0.0',
    from: 'resolve-pathname@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/components'
  },
  {
    name: 'resolve-pathname',
    version: '2.2.0',
    from: 'resolve-pathname@2.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/router'
  }
] 

ssr-window:
[
  {
    name: 'ssr-window',
    version: '2.0.0',
    from: 'ssr-window@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/components/swiper/dom7'
  },
  {
    name: 'ssr-window',
    version: '1.0.1',
    from: 'ssr-window@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/components/swiper'
  }
] 

babel-core:
[
  {
    name: 'babel-core',
    version: '6.26.3',
    from: 'babel-core@6.26.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel'
  },
  {
    name: 'babel-core',
    version: '6.26.0',
    from: 'babel-core@6.26.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  }
] 

lodash:
[
  {
    name: 'lodash',
    version: '4.17.21',
    from: 'lodash@4.17.21',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-generator'
  },
  {
    name: 'lodash',
    version: '4.17.13',
    from: 'lodash@4.17.13',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/router'
  }
] 

convert-source-map:
[
  {
    name: 'convert-source-map',
    version: '1.9.0',
    from: 'convert-source-map@1.9.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core'
  },
  {
    name: 'convert-source-map',
    version: '0.3.5',
    from: 'convert-source-map@^0.3.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/rework'
  }
] 

less:
[
  {
    name: 'less',
    version: '3.13.1',
    from: 'less@^3.10.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-less'
  },
  {
    name: 'less',
    version: '3.0.4',
    from: 'less@3.0.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  }
] 

mime:
[
  {
    name: 'mime',
    version: '1.6.0',
    from: 'mime@^1.4.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-less/less'
  },
  {
    name: 'mime',
    version: '2.6.0',
    from: 'mime@^2.0.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/url-loader'
  }
] 

ansi-regex:
[
  {
    name: 'ansi-regex',
    version: '2.1.1',
    from: 'ansi-regex@2.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/chalk/has-ansi'
  },
  {
    name: 'ansi-regex',
    version: '4.1.1',
    from: 'ansi-regex@^4.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/cliui/strip-ansi'
  },
  {
    name: 'ansi-regex',
    version: '3.0.1',
    from: 'ansi-regex@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/mini-css-extract-plugin/@webpack-contrib/schema-utils/strip-ansi'
  }
] 

strip-ansi:
[
  {
    name: 'strip-ansi',
    version: '3.0.1',
    from: 'strip-ansi@3.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/chalk'
  },
  {
    name: 'strip-ansi',
    version: '5.2.0',
    from: 'strip-ansi@^5.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/cliui'
  },
  {
    name: 'strip-ansi',
    version: '4.0.0',
    from: 'strip-ansi@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/mini-css-extract-plugin/@webpack-contrib/schema-utils'
  }
] 

camelcase:
[
  {
    name: 'camelcase',
    version: '2.1.1',
    from: 'camelcase@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/meow/camelcase-keys'
  },
  {
    name: 'camelcase',
    version: '3.0.0',
    from: 'camelcase@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs/yargs-parser'
  },
  {
    name: 'camelcase',
    version: '5.3.1',
    from: 'camelcase@^5.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/yargs-parser'
  },
  {
    name: 'camelcase',
    version: '1.2.1',
    from: 'camelcase@^1.2.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/adjust-sourcemap-loader'
  },
  {
    name: 'camelcase',
    version: '4.1.0',
    from: 'camelcase@^4.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader'
  }
] 

decamelize:
[
  {
    name: 'decamelize',
    version: '1.2.0',
    from: 'decamelize@^1.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/meow'
  },
  {
    name: 'decamelize',
    version: '2.0.0',
    from: 'decamelize@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  }
] 

parse-json:
[
  {
    name: 'parse-json',
    version: '2.2.0',
    from: 'parse-json@^2.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/meow/read-pkg-up/read-pkg/load-json-file'
  },
  {
    name: 'parse-json',
    version: '4.0.0',
    from: 'parse-json@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-loader/postcss-load-config/cosmiconfig'
  }
] 

string-width:
[
  {
    name: 'string-width',
    version: '1.0.2',
    from: 'string-width@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/npmlog/gauge'
  },
  {
    name: 'string-width',
    version: '3.1.0',
    from: 'string-width@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/cliui'
  },
  {
    name: 'string-width',
    version: '2.1.1',
    from: 'string-width@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/cliui'
  }
] 

is-fullwidth-code-point:
[
  {
    name: 'is-fullwidth-code-point',
    version: '1.0.0',
    from: 'is-fullwidth-code-point@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/npmlog/gauge/string-width'
  },
  {
    name: 'is-fullwidth-code-point',
    version: '2.0.0',
    from: 'is-fullwidth-code-point@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/string-width'
  }
] 

yargs:
[
  {
    name: 'yargs',
    version: '7.1.2',
    from: 'yargs@^7.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph'
  },
  {
    name: 'yargs',
    version: '13.3.2',
    from: 'yargs@^13.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle'
  },
  {
    name: 'yargs',
    version: '12.0.2',
    from: 'yargs@12.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server'
  }
] 

cliui:
[
  {
    name: 'cliui',
    version: '3.2.0',
    from: 'cliui@^3.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs'
  },
  {
    name: 'cliui',
    version: '5.0.0',
    from: 'cliui@^5.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  },
  {
    name: 'cliui',
    version: '4.1.0',
    from: 'cliui@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  }
] 

wrap-ansi:
[
  {
    name: 'wrap-ansi',
    version: '2.1.0',
    from: 'wrap-ansi@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs/cliui'
  },
  {
    name: 'wrap-ansi',
    version: '5.1.0',
    from: 'wrap-ansi@^5.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/cliui'
  }
] 

get-caller-file:
[
  {
    name: 'get-caller-file',
    version: '1.0.3',
    from: 'get-caller-file@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs'
  },
  {
    name: 'get-caller-file',
    version: '2.0.5',
    from: 'get-caller-file@^2.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  }
] 

os-locale:
[
  {
    name: 'os-locale',
    version: '1.4.0',
    from: 'os-locale@^1.4.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs'
  },
  {
    name: 'os-locale',
    version: '3.1.0',
    from: 'os-locale@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  }
] 

lcid:
[
  {
    name: 'lcid',
    version: '1.0.0',
    from: 'lcid@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs/os-locale'
  },
  {
    name: 'lcid',
    version: '2.0.0',
    from: 'lcid@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale'
  }
] 

invert-kv:
[
  {
    name: 'invert-kv',
    version: '1.0.0',
    from: 'invert-kv@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs/os-locale/lcid'
  },
  {
    name: 'invert-kv',
    version: '2.0.0',
    from: 'invert-kv@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale/lcid'
  }
] 

require-main-filename:
[
  {
    name: 'require-main-filename',
    version: '1.0.1',
    from: 'require-main-filename@^1.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs'
  },
  {
    name: 'require-main-filename',
    version: '2.0.0',
    from: 'require-main-filename@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  }
] 

which-module:
[
  {
    name: 'which-module',
    version: '1.0.0',
    from: 'which-module@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs'
  },
  {
    name: 'which-module',
    version: '2.0.1',
    from: 'which-module@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  }
] 

yargs-parser:
[
  {
    name: 'yargs-parser',
    version: '5.0.1',
    from: 'yargs-parser@^5.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/node-sass/sass-graph/yargs'
  },
  {
    name: 'yargs-parser',
    version: '13.1.2',
    from: 'yargs-parser@^13.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  },
  {
    name: 'yargs-parser',
    version: '10.1.0',
    from: 'yargs-parser@^10.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  }
] 

promise:
[
  {
    name: 'promise',
    version: '8.3.0',
    from: 'promise@^8.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle'
  },
  {
    name: 'promise',
    version: '7.3.1',
    from: 'promise@^7.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/less'
  }
] 

stylus:
[
  {
    name: 'stylus',
    version: '0.54.8',
    from: 'stylus@^0.54.5',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus'
  },
  {
    name: 'stylus',
    version: '0.54.5',
    from: 'stylus@0.54.5',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  }
] 

css-parse:
[
  {
    name: 'css-parse',
    version: '2.0.0',
    from: 'css-parse@~2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus/stylus'
  },
  {
    name: 'css-parse',
    version: '1.7.0',
    from: 'css-parse@1.7.x',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/stylus'
  }
] 

uglify-js:
[
  {
    name: 'uglify-js',
    version: '3.19.3',
    from: 'uglify-js@^3.3.24',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/plugin-uglifyjs'
  },
  {
    name: 'uglify-js',
    version: '3.4.10',
    from: 'uglify-js@3.4.x',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/html-minifier'
  }
] 

prop-types:
[
  {
    name: 'prop-types',
    version: '15.8.1',
    from: 'prop-types@^15.6.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/taro-alipay'
  },
  {
    name: 'prop-types',
    version: '15.7.2',
    from: 'prop-types@15.7.2',
    path: 'muwa1-ts-project/@mu/zui'
  }
] 

postcss:
[
  {
    name: 'postcss',
    version: '6.0.23',
    from: 'postcss@^6.0.23',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/autoprefixer'
  },
  {
    name: 'postcss',
    version: '5.2.18',
    from: 'postcss@^5.2.10',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-pxtransform/postcss-pxtorem'
  }
] 

p-try:
[
  {
    name: 'p-try',
    version: '1.0.0',
    from: 'p-try@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up/locate-path/p-locate/p-limit'
  },
  {
    name: 'p-try',
    version: '2.2.0',
    from: 'p-try@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/p-limit'
  }
] 

webpack-log:
[
  {
    name: 'webpack-log',
    version: '2.0.0',
    from: 'webpack-log@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin'
  },
  {
    name: 'webpack-log',
    version: '1.2.0',
    from: 'webpack-log@^1.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/mini-css-extract-plugin/@webpack-contrib/schema-utils'
  }
] 

async:
[
  {
    name: 'async',
    version: '2.6.4',
    from: 'async@^2.5.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/csso-webpack-plugin'
  },
  {
    name: 'async',
    version: '3.2.6',
    from: 'async@^3.2.6',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/portfinder'
  }
] 

domhandler:
[
  {
    name: 'domhandler',
    version: '4.3.1',
    from: 'domhandler@^4.3.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/htmlparser2'
  },
  {
    name: 'domhandler',
    version: '2.4.2',
    from: 'domhandler@^2.4.2',
    path: 'muwa1-ts-project/@mu/zui/@mu/mini-html-parser2'
  }
] 

clone:
[
  {
    name: 'clone',
    version: '2.1.2',
    from: 'clone@^2.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/less-loader'
  },
  {
    name: 'clone',
    version: '1.0.4',
    from: 'clone@^1.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/ora/wcwidth/defaults'
  }
] 

mimic-fn:
[
  {
    name: 'mimic-fn',
    version: '1.2.0',
    from: 'mimic-fn@^1.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/ora/cli-cursor/restore-cursor/onetime'
  },
  {
    name: 'mimic-fn',
    version: '2.1.0',
    from: 'mimic-fn@^2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale/mem'
  }
] 

lodash.defaults:
[
  {
    name: 'lodash.defaults',
    version: '3.1.2',
    from: 'lodash.defaults@^3.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/adjust-sourcemap-loader'
  },
  {
    name: 'lodash.defaults',
    version: '4.2.0',
    from: 'lodash.defaults@^4.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader'
  }
] 

lodash.assign:
[
  {
    name: 'lodash.assign',
    version: '3.2.0',
    from: 'lodash.assign@^3.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/adjust-sourcemap-loader/lodash.defaults'
  },
  {
    name: 'lodash.assign',
    version: '4.2.0',
    from: 'lodash.assign@^4.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/adjust-sourcemap-loader'
  }
] 

array-flatten:
[
  {
    name: 'array-flatten',
    version: '2.1.2',
    from: 'array-flatten@^2.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/bonjour'
  },
  {
    name: 'array-flatten',
    version: '1.1.1',
    from: 'array-flatten@1.1.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express'
  }
] 

negotiator:
[
  {
    name: 'negotiator',
    version: '0.6.4',
    from: 'negotiator@~0.6.4',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/compression'
  },
  {
    name: 'negotiator',
    version: '0.6.3',
    from: 'negotiator@0.6.3',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/accepts'
  }
] 

depd:
[
  {
    name: 'depd',
    version: '2.0.0',
    from: 'depd@2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/body-parser'
  },
  {
    name: 'depd',
    version: '1.1.2',
    from: 'depd@~1.1.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/serve-index/http-errors'
  }
] 

http-errors:
[
  {
    name: 'http-errors',
    version: '2.0.0',
    from: 'http-errors@2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/body-parser'
  },
  {
    name: 'http-errors',
    version: '1.6.3',
    from: 'http-errors@~1.6.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/serve-index'
  }
] 

encodeurl:
[
  {
    name: 'encodeurl',
    version: '2.0.0',
    from: 'encodeurl@~2.0.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express'
  },
  {
    name: 'encodeurl',
    version: '1.0.2',
    from: 'encodeurl@~1.0.2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/send'
  }
] 

statuses:
[
  {
    name: 'statuses',
    version: '2.0.1',
    from: 'statuses@2.0.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/finalhandler'
  },
  {
    name: 'statuses',
    version: '1.5.0',
    from: 'statuses@>= 1.4.0 < 2',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/serve-index/http-errors'
  }
] 

setprototypeof:
[
  {
    name: 'setprototypeof',
    version: '1.2.0',
    from: 'setprototypeof@1.2.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/http-errors'
  },
  {
    name: 'setprototypeof',
    version: '1.1.0',
    from: 'setprototypeof@1.1.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/serve-index/http-errors'
  }
] 

faye-websocket:
[
  {
    name: 'faye-websocket',
    version: '0.10.0',
    from: 'faye-websocket@^0.10.0',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/sockjs'
  },
  {
    name: 'faye-websocket',
    version: '0.11.4',
    from: 'faye-websocket@~0.11.1',
    path: 'muwa1-ts-project/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/sockjs-client'
  }
] 
import Taro, { Component, Config } from '@tarojs/taro';
import { configure } from 'mobx';
import { setup } from '@mu/business-basic';
import setupConfig from './constants/setupConfig';
import Index from './pages/index/index';

import './app.scss';

/**
 * 应用入口文件 - DDD 架构初始化
 *
 * 负责应用的全局配置和初始化工作，包括：
 * 1. 业务基础库的初始化配置
 * 2. MobX 状态管理的配置
 * 3. 全局样式的引入
 * 4. 应用级别的配置定义
 *
 * 设计说明：
 * - 这里是整个 DDD 架构的入口点
 * - 配置了 MobX 的严格模式，确保状态变更的可追踪性
 * - 通过 setup 函数初始化业务基础库
 */

// TODO，报错，需框架改@郭苗苗
setup(setupConfig);

// 配置 MobX：启用严格模式，确保所有状态变更都通过 action 进行
configure({ enforceActions: 'observed' });

class App extends Component {
  /**
   * 指定config的类型声明为: Taro.Config
   *
   * 由于 typescript 对于 object 类型推导只能推出 Key 的基本类型
   * 对于像 navigationBarTextStyle: 'black' 这样的推导出的类型是 string
   * 提示和声明 navigationBarTextStyle: 'black' | 'white' 类型冲突, 需要显示声明类型
   */
  // eslint-disable-next-line react/sort-comp
  config: Config = {
    pages: [
      'pages/homepage/index',
      'pages/index/index',
    ],
    window: {
      backgroundTextStyle: 'light',
      navigationBarBackgroundColor: '#fff',
      navigationBarTitleText: 'WeChat',
      navigationBarTextStyle: 'black',
    },
    permission: {
      'scope.userLocation': {
        desc: '你的位置信息将用于小程序位置接口的效果展示',
      },
    },
  };

  componentWillMount() {
  }

  componentDidMount() { }

  componentDidShow() { }

  componentDidHide() { }

  componentCatchError() { }

  componentDidCatchError() { }

  // 在 App 类中的 render() 函数没有实际作用
  // 请勿修改此函数
  render() {
    return (
      <Index />
    );
  }
}

Taro.render(<App />, document.getElementById('app'));

import { observable, action } from 'mobx';
import type { UserInfo } from '@src/types/user.d';

/**
 * 用户实体类 - DDD 领域层核心实体
 *
 * 作为聚合根，用户实体封装了用户相关的所有数据和业务规则。
 * 这是一个充血的数据对象，包含数据和业务规则，且数据和业务规则是内聚的。
 *
 * 设计原则：
 * 1. 实体具有唯一标识（userId）
 * 2. 封装业务逻辑，确保数据一致性
 * 3. 通过 MobX 实现响应式数据绑定
 * 4. 提供统一的状态更新接口
 * 5. 采用单例模式，确保全局用户状态一致性
 *
 * @example
 * ```typescript
 * const user = getUser();
 * user.setState({ name: '张三', isLogin: true });
 * ```
 */

export class User implements UserInfo {
  /** 用户ID（hash值，用于前端展示） */
  @observable userId = '';

  /** 用户头像（base64格式） */
  @observable avatar = '';

  /** 用户昵称或姓名 */
  @observable name = '';

  /** 登录状态 */
  @observable isLogin = false;

  /** 实名认证状态 */
  @observable isIdentity = false;

  /** 绑定的银行卡数量 */
  @observable bankcardCount = 0;

  /** 当前自信分 */
  @observable selfScore = 0;

  /** 总自信分 */
  @observable totalSelfScore = 0;

  /** 客户ID（hash值） */
  @observable custId = '';

  /** 用户ID（原始值，用于后端交互） */
  @observable _userId = '';

  /** 手机号 */
  @observable mobile = '';

  /** 头像ID（用于获取头像） */
  avatarId = '';

  /** 用户标签列表 */
  @observable userTagCodeList = [];

  /**
   * 批量更新用户状态
   *
   * 这是用户实体的核心业务方法，负责：
   * 1. 批量更新用户属性
   * 2. 数据适配和转换
   * 3. 确保数据一致性
   *
   * @param props 要更新的用户属性
   */
  @action.bound
  setState = (props: Partial<UserInfo>) => {
    Object.keys(props || {}).forEach((k) => {
      if (props[k] !== this[k]) {
        this[k] = this.adapterProps(k, props[k]);
      }
    });
  };

  /**
   * 属性适配器 - 处理特殊字段的数据转换
   *
   * 负责将后端数据转换为前端可用的格式，
   * 体现了实体内部的业务规则。
   *
   * @param key 属性名
   * @param val 属性值
   * @returns 转换后的属性值
   */
  adapterProps = (key: string, val: any): any => {
    let result = val;

    // 头像数据格式化：确保头像为完整的 base64 格式
    if (key === 'avatar') {
      if (val && !/^data:image\//.test(val)) {
        result = `data:image/png;base64,${val}`;
      }
    }

    return result;
  };
}

// 用户实体单例实例
let userInstance: User | null = null;

/**
 * 获取用户单例实例
 *
 * 采用单例模式确保全局用户状态的一致性。
 * 这是获取用户实体的唯一入口，避免创建多个用户实例。
 *
 * 使用场景：
 * - 在领域服务中操作用户数据
 * - 在控制层 Store 中获取用户状态
 * - 在视图层中展示用户信息
 *
 * @returns User实例 - 全局唯一的用户实体实例
 *
 * @example
 * ```typescript
 * // 在领域服务中使用
 * const user = getUser();
 * user.setState({ isLogin: true });
 *
 * // 在 Store 中使用
 * @observable user = getUser();
 * ```
 */
export const getUser = (): User => {
  if (!userInstance) {
    userInstance = new User();
  }
  return userInstance;
};

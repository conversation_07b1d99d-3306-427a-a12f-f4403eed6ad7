/**
 * 用户标签枚举
 *
 * 定义了用户的各种业务标签，用于：
 * - 业务逻辑判断
 * - 个性化展示
 * - 权限控制等
 */
export enum EUserTag {
  /** 站内导流客户标签（客户运营主体标签为ZL2/ZL3） */
  DIVERSION_IN = '1',
}

/**
 * 用户相关类型定义
 *
 * 定义了用户领域相关的所有类型接口和枚举。
 * 这些类型在整个应用中保持一致，确保类型安全。
 */

/**
 * 用户信息接口
 *
 * 定义了用户实体的完整数据结构，包含：
 * - 基础身份信息（ID、姓名、手机号等）
 * - 状态信息（登录状态、实名状态等）
 * - 业务信息（银行卡数量、自信分等）
 * - 扩展信息（头像、标签等）
 */
export interface UserInfo {
  /** 用户ID（hash值，用于前端展示和日志记录） */
  userId: string;

  /** 用户ID（原始值，用于后端接口调用） */
  _userId: string;

  /** 客户ID（hash值） */
  custId: string;

  /** 用户头像（base64格式的图片数据） */
  avatar: string;

  /** 头像ID（用于从服务器获取头像） */
  avatarId: string;

  /** 用户显示名称（昵称或真实姓名） */
  name: string;

  /** 登录状态标识 */
  isLogin: boolean;

  /** 实名认证状态标识 */
  isIdentity: boolean;

  /** 绑定的银行卡数量 */
  bankcardCount: number;

  /** 当前自信分数值 */
  selfScore: number;

  /** 历史累计自信分总值 */
  totalSelfScore: number;

  /** 手机号码 */
  mobile: string;

  /** 用户标签列表（用于业务逻辑判断） */
  userTagCodeList: Array<EUserTag>;

  /** 状态更新方法（可选，用于实体类） */
  setState?: (props: Partial<UserInfo>) => void;
}

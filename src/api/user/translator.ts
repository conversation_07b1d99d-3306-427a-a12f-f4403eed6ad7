import { isMuapp } from '@mu/madp-utils';
import { NOT_REAL_NAME_TEXT } from '@constants/index';
import { EUserTag } from '@src/types/user.d';

/**
 * 用户数据转换器 - DDD 数据接口层
 *
 * 数据转换层负责将后端返回的数据"清洗"，改造成更直观的字段(key)、
 * 更方便使用的数据(value)。避免了后端定义字段不规范、混乱对前端的影响。
 *
 * 职责范围：
 * 1. 字段名称转换：将后端字段转换为前端语义化字段
 * 2. 数据格式化：处理特殊数据格式（如头像、姓名等）
 * 3. 业务逻辑适配：根据不同平台和业务规则进行数据适配
 * 4. 数据清洗：过滤和转换不符合前端要求的数据
 *
 * 设计原则：
 * - 纯函数：输入相同则输出相同，无副作用
 * - 单一职责：只负责数据转换，不包含业务逻辑
 * - 语义化：转换后的字段名称具有明确的业务含义
 */

/**
 * 用户信息数据转换器
 *
 * 将后端返回的用户信息转换为前端可直接使用的格式。
 * 处理用户姓名显示逻辑、用户标签转换等复杂业务规则。
 *
 * 转换规则：
 * 1. 姓名显示：优先使用昵称，其次使用客户姓名
 * 2. 平台适配：根据不同平台（APP/H5）调整显示逻辑
 * 3. 标签转换：将后端标签代码转换为前端枚举值
 * 4. 字段映射：将后端字段映射为语义化的前端字段
 *
 * @param param0 后端返回的用户信息原始数据
 * @param param0.basicCustDto 基础客户信息
 * @param param0.loginSuccess 登录成功标识
 * @param param0.userTagList 用户标签列表
 * @returns 转换后的用户信息对象
 */
export function userInfoTranslator({
  basicCustDto,
  loginSuccess,
  userTagList,
}: {
  basicCustDto?: any;
  loginSuccess?: boolean;
  userTagList?: any[];
}) {
  // 解构后端返回的基础客户信息
  const {
    userIdHash, identified,
    nickName, headImageId,
    custName, mobile,
    custIdHash, userId,
  } = basicCustDto || {};

  // 处理客户姓名：如果没有客户姓名，使用默认文本
  const custNameDisplay = custName || NOT_REAL_NAME_TEXT;
  let name = nickName;

  // 用户姓名显示逻辑：
  // 1. 如果没有昵称或昵称是默认格式，使用客户姓名
  if (!nickName || nickName === `小鹿${mobile}`) {
    name = custNameDisplay;
  }

  // 2. H5环境下，如果昵称包含星号（脱敏），使用客户姓名
  if (!isMuapp() && (!nickName || nickName.indexOf('*') > -1)) {
    name = custNameDisplay;
  }

  // 3. 如果姓名包含星号且长度超过4位，只显示后4位
  if (name.indexOf('*') > -1 && name.length > 4) {
    name = name.slice(-4);
  }

  // 用户标签转换：将后端标签转换为前端枚举值
  const tagList: Array<EUserTag> = [];
  (userTagList || []).forEach((item) => {
    // 客户运营主体标签：ZL2/ZL3 表示站内导流客户
    if (item.tagCode === 'U_CUST_MAIN' && (item.tagValue === 'ZL2' || item.tagValue === 'ZL3')) {
      tagList.push(EUserTag.DIVERSION_IN);
    }
  });

  // 返回转换后的用户信息对象
  return {
    name: name || '', // 用户显示名称
    userId: userIdHash || '', // 用户ID（hash值）
    isIdentity: identified || false, // 实名认证状态
    isLogin: loginSuccess || false, // 登录状态
    avatarId: headImageId || '', // 头像ID
    custId: custIdHash || '', // 客户ID（hash值）
    _userId: userId || '', // 用户ID（原始值）
    mobile: mobile || '', // 手机号
    userTagCodeList: tagList || [], // 转换后的用户标签列表
  };
}

/**
 * 用户头像数据转换器
 *
 * 将后端返回的头像内容转换为前端可用的格式。
 * 简单的字段映射，将后端字段名转换为前端语义化字段名。
 *
 * @param param0 后端返回的头像数据
 * @param param0.headImageContent 头像内容（base64格式）
 * @returns 转换后的头像对象
 */
export function avatarTranslator({
  headImageContent,
}: {
  headImageContent?: string;
}) {
  return {
    avatar: headImageContent, // 头像内容（base64格式）
  };
}

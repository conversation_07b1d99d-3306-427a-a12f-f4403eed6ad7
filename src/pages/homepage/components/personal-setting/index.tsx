import { Component } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import { MUView, MUImage, MUText } from '@mu/zui';
import defaultImg from '@assets/img/selfie_3x.png';
import { doLoginH5 } from '@utils/h5-login';
import { UserInfo } from '@src/types/user.d';
import './index.scss';

/**
 * 个人设置组件属性接口
 */
type Props = {
  /** 用户信息对象 */
  user: UserInfo;
};

/**
 * 个人设置组件 - DDD 视图层组件
 *
 * 负责展示用户的个人信息，包括：
 * - 用户头像展示
 * - 用户名称显示（根据登录状态）
 * - 手机号展示
 * - 登录引导功能
 *
 * 设计原则：
 * - 纯展示组件：只负责数据展示，不包含业务逻辑
 * - 响应式：通过 @observer 实现数据变化时自动更新
 * - 可复用：通过 props 接收数据，可在不同页面复用
 *
 * @example
 * ```tsx
 * <PersonalSetting user={userInfo} />
 * ```
 */
@observer
export default class PersonalSetting extends Component<Props, {}> {
  /**
   * 处理登录点击事件
   *
   * 当用户未登录时，点击登录按钮触发此方法。
   * 调用工具函数进行 H5 登录跳转，登录成功后回到当前页面。
   */
  handleLogin = () => {
    doLoginH5({ redirectUrl: window.location.href });
  };

  /**
   * 渲染组件
   *
   * 根据用户登录状态展示不同的内容：
   * - 已登录：显示用户名和手机号
   * - 未登录：显示"未登录"和登录按钮
   */
  render() {
    const { user } = this.props;

    return (
      <MUView
        className="personal__setting__wrap"
        style="background-color: #FFF"
      >
        <MUView className="personal__setting__wrap__left">
          {/* 用户头像 */}
          <MUImage
            src={defaultImg}
            className="personal__setting__wrap__left__sefticon"
          />

          {/* 用户信息区域 */}
          <MUView className="personal__setting__wrap__left__info">
            {/* 用户名称区域 */}
            <MUView className="personal__setting__wrap__left__info__username">
              {user.isLogin ? (user.name || '****') : '未登录'}
              {!user.isLogin && (
                <MUText
                  className="personal__setting__wrap__left__info__login-btn"
                  onClick={this.handleLogin}
                >
                  点击跳转登录
                </MUText>
              )}
            </MUView>

            {/* 手机号展示 */}
            <MUView className="personal__setting__wrap__left__info__phone">
              {user.mobile}
            </MUView>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}

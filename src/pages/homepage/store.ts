import { observable } from 'mobx';
import { getUser } from '@models/user/user';
import { UserService } from '@services/user-service';

/**
 * 首页控制层 Store - DDD 控制层
 *
 * 控制层负责管理 UI 状态、处理用户交互、对领域对象和领域服务进行编排和组合。
 * 一般不包含具体的业务逻辑，业务逻辑应该放在领域层中。
 *
 * 职责范围：
 * 1. 管理页面级别的 UI 状态
 * 2. 协调领域服务的调用
 * 3. 为视图层提供数据和操作接口
 * 4. 处理页面生命周期相关的逻辑
 *
 * 设计原则：
 * - 薄控制层：不包含复杂的业务逻辑
 * - 编排协调：负责协调多个领域服务的调用
 * - 状态管理：管理页面相关的 UI 状态
 * - 响应式：通过 MobX 实现响应式数据绑定
 *
 * @example
 * ```typescript
 * // 在视图层中使用
 * const { user } = HomepageStore;
 *
 * // 初始化页面数据
 * await HomepageStore.init();
 * ```
 */
class HomepageStore {
  /** 用户实体实例 - 通过领域层获取 */
  @observable user = getUser();

  /**
   * 页面初始化方法
   *
   * 负责协调用户相关数据的获取，包括：
   * 1. 获取用户基本信息
   * 2. 根据头像ID获取用户头像
   *
   * 这是一个编排方法，协调多个领域服务的调用，
   * 不包含具体的业务逻辑。
   *
   * @returns 更新后的用户实体
   * @throws 当任何一个服务调用失败时抛出异常
   */
  async init() {
    // 获取用户基本信息
    await UserService.getUserInfo();

    // 如果有头像ID，获取用户头像
    if (this.user.avatarId) {
      await UserService.getUserAvatar(this.user.avatarId);
    }

    return this.user;
  }
}

// 导出单例实例
const homepageStore = new HomepageStore();
export default homepageStore;

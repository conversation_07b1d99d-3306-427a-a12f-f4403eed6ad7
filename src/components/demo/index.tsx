import { Component } from '@tarojs/taro';
import {
  observer, inject,
} from '@tarojs/mobx';
import { MUView } from '@mu/zui';

type Props = {
  indexStore: {
    localThing: string;
    localComputedThing: string;
  };
};

type State = {
};

@inject('indexStore')
@observer
class CompDemo extends Component<Props, State> {
  async componentDidMount() {
  }

  render() {
    const {
      indexStore: {
        localThing,
        localComputedThing,
      },
    } = this.props || {};
    return (
      <MUView>
        <MUView>{localThing}</MUView>
        <MUView>{localComputedThing}</MUView>
      </MUView>
    );
  }
}

export default CompDemo;

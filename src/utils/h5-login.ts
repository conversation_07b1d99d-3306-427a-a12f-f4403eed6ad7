import Madp from '@mu/madp';
import {
  isMuapp,
  isAlipay,
} from '@mu/madp-utils';
import { urlDomain } from '@mu/business-basic';

const actions = (options = {}) => {
  const channel = Madp.getChannel();
  const redirectUrl = encodeURIComponent(options.redirectUrl || window.location.href);

  const muappLogin = () => {
    if (options && options.appUseJumpUrl) {
      // 手机号不一致换登录使用
      Madp.nativeNavigateTo({
        useAppRouter: true,
        url: 'muapp://commonlogin/mainPage',
      });
      Madp.closeWebview();
    } else {
      const onLoginSuccess = () => window.location.reload();
      // 可选择传pushParam给APP注册登录组件，一旦传goBack参数，则从哪里来回哪里去，不会出现默认的注册成功引导申请小额现金贷界面，防止其他申请流程无法进入
      const goBackParam = options.goBack ? 'goBack' : '';
      return window.muapp.LoginRegisterPlugin.pushLogin(onLoginSuccess, goBackParam);
    }
  };

  // 联合登录(微信、支付宝、标准第三方、M版)
  const unionLogin = () => {
    // 如果是支付宝环境，且是非支付宝申请入口
    const loginChannel = isAlipay() && channel.indexOf('ZFB') === -1;
    const url = loginChannel ? `${urlDomain}/2ZFB/loginregister/#/auth?mgpAuth=1&redirectUrl=${redirectUrl}` : `${urlDomain}/${channel}/loginregister/#/auth?mgpAuth=1&redirectUrl=${redirectUrl}`;
    window.location.replace(url);
  };

  // 招行手机银行
  const cmbLogin = () => {
    const cmbLoginUrl = 'http://cmbls/functionjump?id=functionjump&action=gofuncid&funcid=130253&needlogin=true&loginmode=d&clean=false&data=0&CloseCurrentView=true';
    window.location.replace(cmbLoginUrl);
  };

  return new Map([
    [isMuapp(), muappLogin],
    [channel === '3CMBAPP', cmbLogin],
    ['default', unionLogin],
  ]);
};

const jumpLoginAction = (options) => {
  const action = actions(options).get(true);
  if (action) {
    action.call(this);
  } else {
    // use default mwb login
    actions(options).get('default').call(this);
  }
};

export {
  jumpLoginAction as doLoginH5,
};

# 依赖分析工具 (Dependency Analyzer)

一个集成的命令行工具，用于分析 TypeScript/JavaScript 项目的包依赖关系并检测版本冲突。

## 功能特性

- **实际使用包分析**：使用 depcheck 分析项目代码中实际引用的 npm 包
- **递归依赖分析**：深入分析每个包的嵌套依赖关系
- **版本冲突检测**：识别项目中存在的包版本冲突
- **多种输出格式**：支持控制台、JSON、YAML 格式输出
- **智能过滤**：自动过滤 webpack 别名，只关注真实的包依赖

## 安装

该工具已集成在项目中，无需额外安装。确保项目已安装以下依赖：

```bash
npm install depcheck
```

## 使用方法

### 基本用法

```bash
# 运行完整分析并在控制台显示结果
node dependency-analyzer.js

# 显示详细输出
node dependency-analyzer.js --verbose

# 显示帮助信息
node dependency-analyzer.js --help
```

### 输出格式选项

```bash
# 输出为 JSON 格式
node dependency-analyzer.js --output-format=json --output-file=report.json

# 输出为 YAML 格式
node dependency-analyzer.js --output-format=yaml --output-file=report.yaml

# 控制台输出（默认）
node dependency-analyzer.js --output-format=console
```

### 命令行参数

| 参数 | 简写 | 描述 | 默认值 |
|------|------|------|--------|
| `--help` | `-h` | 显示帮助信息 | - |
| `--verbose` | `-v` | 显示详细输出 | false |
| `--exclude` | `-e` | 排除的包名，用逗号分隔 | - |
| `--output-format` | `-f` | 输出格式 (console/json/yaml) | console |
| `--output-file` | `-o` | 输出文件路径 | - |

## 分析阶段

### 阶段1：实际使用包分析
- 使用 depcheck 扫描项目源代码
- 识别实际被引用的 npm 包
- 过滤掉 webpack 别名（如 @src、@api 等）
- 只分析 dependencies 中的包

### 阶段2：递归依赖分析
- 对每个实际使用的包进行深度分析
- 递归遍历所有层级的依赖关系
- 构建完整的依赖树结构

### 阶段3：版本冲突检测
- 分析 package-lock.json 文件
- 识别同一包的不同版本
- 提供冲突包的详细路径信息

## 输出格式

### 控制台输出
```
📊 依赖分析报告
==================================================

📈 汇总信息:
   实际使用的包数量: 3
   版本冲突数量: 162
   分析耗时: 6716ms

📦 实际使用的包:
   @mu/basic-library@1.24.0-beta.20 (dependency)
   @mu/local-config@1.6.2-beta.5 (dependency)
   @mu/zui@1.24.3-beta.37 (dependency)

⚠️  版本冲突:
   file-type: 7个版本冲突
   globby: 3个版本冲突
   ...
```

### JSON 输出
```json
{
  "actualUsedPackages": [
    {
      "name": "@mu/basic-library",
      "version": "1.24.0-beta.20",
      "type": "dependency"
    }
  ],
  "nestedDependencies": {
    "@mu/basic-library": {}
  },
  "versionConflicts": {
    "file-type": [
      {
        "name": "file-type",
        "version": "12.4.2",
        "from": "file-type@^12.0.0",
        "path": "..."
      }
    ]
  }
}
```

### YAML 输出
```yaml
actualUsedPackages:
- name: @mu/basic-library
  version: 1.24.0-beta.20
  type: dependency
nestedDependencies:
  @mu/basic-library: {}
versionConflicts:
  file-type:
  - name: file-type
    version: 12.4.2
    from: file-type@^12.0.0
    path: ...
```

## 解决版本冲突

当检测到版本冲突时，可以尝试以下解决方案：

TODO

## 技术实现

- **depcheck**：分析实际使用的包
- **npm ls**：获取依赖树信息
- **package-lock.json**：版本冲突检测
- **webpack 配置**：过滤别名路径

## 注意事项

1. 确保在项目根目录运行工具
2. 需要存在 package.json 和 package-lock.json 文件
3. 工具会自动过滤 webpack 别名，只分析真实的 npm 包
4. 大型项目的分析可能需要较长时间

## 故障排除

### 常见问题

**Q: 工具报告 0 个实际使用的包**
A: 检查是否在正确的项目目录中运行，确保存在 src 目录和源代码文件

**Q: 版本冲突数量很多**
A: 这是正常现象，可以运行 `npm dedupe` 尝试减少冲突

**Q: 输出文件无法生成**
A: 检查输出目录的写入权限，确保路径存在

### 调试模式

使用 `--verbose` 参数可以看到更详细的执行信息：

```bash
node dependency-analyzer.js --verbose
```

## 更新日志

- v1.0.0: 初始版本，集成三个分析阶段
- 支持多种输出格式
- 智能过滤 webpack 别名
